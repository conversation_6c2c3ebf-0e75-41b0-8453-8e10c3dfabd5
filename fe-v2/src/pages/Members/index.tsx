import React from 'react'
import { useModel } from '@umijs/max'
import { UserOutlined } from '@ant-design/icons'
import AddressCopy from '@/components/AddressCopy'
import { useWallet } from '@solana/wallet-adapter-react'
import { Card, Space, Tag, Typography, Spin } from 'antd'

const { Text } = Typography

const getPermissionColor = (permission: string) => {
  if (permission === 'Proposer') return '#52c41a' // 绿色
  if (permission === 'Voter') return '#1890ff' // 蓝色
  if (permission === 'Executor') return '#fa8c16' // 橙色
}

const MembersPage: React.FC = () => {
  const { publicKey } = useWallet()
  const { currentMultisig, loading } = useModel('multisig')
  const isCurrentUser = (memberAddress: string) => publicKey?.toBase58() === memberAddress

  if (loading) {
    return (
      <div style={{ padding: 24 }}>
        <Card>
          <div style={{ textAlign: 'center', padding: '60px 0' }}>
            <Spin size="large" />
            <p style={{ color: '#999', margin: '16px 0 0 0' }}>加载成员信息中...</p>
          </div>
        </Card>
      </div>
    )
  }

  return (
    <div>
      <Space direction="vertical" size="middle" style={{ width: '100%' }}>
        {currentMultisig!.members.filter(({ permissions }) => {
          return !(permissions.length === 1 && permissions[0] === 'Proposer')
        }).map(({ address, permissions }) => {
          return (
            <Card key={address} size="small" style={{ backgroundColor: isCurrentUser(address) ? '#f0f8ff' : '#fff', border: isCurrentUser(address) ? '1px solid #1890ff' : '1px solid #d9d9d9', borderRadius: 8 }}>
              <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <div style={{ display: 'flex', alignItems: 'center', gap: 16, flex: 1 }}>
                  <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
                    <UserOutlined style={{ color: '#1890ff', fontSize: 16 }} />
                    <Text strong>成员</Text>
                  </div>
                  <div style={{ background: '#f5f5f5', padding: '6px 12px', borderRadius: 6, border: '1px solid #e8e8e8', fontSize: 12, fontFamily: 'monospace', flex: 1, maxWidth: 400 }}>
                    <AddressCopy address={address || ''} showFullAddress={true} />
                  </div>
                  {isCurrentUser(address) && <Tag color="purple">当前用户</Tag>}
                </div>
                <div>
                  <Space wrap size={[4, 4]}>
                    {permissions.map((label: any) => (
                      <Tag key={label} color={getPermissionColor(label)} style={{ margin: 0, fontSize: 11, padding: '2px 6px', borderRadius: 4 }}>
                        {label}
                      </Tag>
                    ))}
                  </Space>
                </div>
              </div>
            </Card>
          )
        })}
      </Space>
    </div>
  )
}

export default MembersPage