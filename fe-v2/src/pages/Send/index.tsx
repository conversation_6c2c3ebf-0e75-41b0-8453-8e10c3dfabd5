import * as multisig from '@sqds/multisig'
import { history, useModel } from '@umijs/max'
import { SendOutlined } from '@ant-design/icons'
import React, { useState, useCallback, useEffect } from 'react'
import { useWallet, useConnection } from '@solana/wallet-adapter-react'
import { Form, Input, Button, Card, Select, message, InputNumber, Space } from 'antd'
import { TOKEN_PROGRAM_ID, getAssociatedTokenAddress, createTransferInstruction } from '@solana/spl-token'
import { PublicKey, SystemProgram, TransactionMessage, VersionedTransaction, LAMPORTS_PER_SOL } from '@solana/web3.js'
import Api from '@/services/api'
import { SQUADS_PROGRAM_ID_V4_PUBKEY } from '@/constants'

const { Option } = Select

const SendPage: React.FC = () => {
  const [form] = Form.useForm()
  const { connection } = useConnection()
  const [loading, setLoading] = useState(false)
  const { publicKey, signTransaction } = useWallet()
  const [recipients, setRecipients] = useState<[]>([])
  const [messageApi, contextHolder] = message.useMessage()
  const [recipientsLoading, setRecipientsLoading] = useState(false)
  const { currentMultisig, loading: multisigLoading, refreshMultisigs } = useModel('multisig')

  useEffect(() => {
    refreshMultisigs()
  }, [])

  const loadRecipients = useCallback(async () => {
    setRecipientsLoading(true)
    const tos = await Api.getRecipients()
    setRecipients(tos)
    setRecipientsLoading(false)
  }, [])

  useEffect(() => {
    loadRecipients()
  }, [loadRecipients])

  const handleTransfer = useCallback(async (values: any) => {
    try {
      setLoading(true)
      const { recipient, amount, selectedToken } = values

      if (!publicKey) throw new Error('请先连接钱包')
      if (!currentMultisig) throw new Error('多签账户未加载')
      const currentUser = currentMultisig.members.find(member => member.address === publicKey.toBase58())
      if (!currentUser) throw new Error('当前钱包不是多签成员')
      if (!currentUser.permissions.includes('Proposer')) throw new Error('您没有创建转账交易的权限（需要 Proposer 权限）')
      if (!recipient) throw new Error('请选择收款地址')
      if (!amount || parseFloat(amount) <= 0) throw new Error('请输入有效的转账金额')
      if (!selectedToken) throw new Error('请选择币种')

      const recipientPubkey = new PublicKey(recipient)
      const vaultPda = new PublicKey(currentMultisig.vault.address)
      const multisigPda = new PublicKey(currentMultisig.multisigAccount)

      // 构建交易数据
      let transferInstruction
      if (selectedToken === 'sol') {
        const transferAmount = Math.floor(parseFloat(amount) * LAMPORTS_PER_SOL)
        if (currentMultisig.vault.balance < transferAmount) throw new Error(`sol余额不足。当前余额: ${currentMultisig.vault.balanceSOL} sol`)
        transferInstruction = SystemProgram.transfer({
          fromPubkey: vaultPda,
          toPubkey: recipientPubkey,
          lamports: transferAmount, // 包含精度
        })
      } else {
        const selectedAsset = currentMultisig.vault.assets.find(asset => asset.symbol === selectedToken)
        if (!selectedAsset || !selectedAsset.mint || selectedAsset.decimals === undefined) throw new Error('未找到对应的Token信息')

        const transferAmount = Math.floor(parseFloat(amount) * Math.pow(10, selectedAsset.decimals))

        if (selectedAsset.balance < parseFloat(amount)) throw new Error(`${selectedAsset.symbol}余额不足。当前余额: ${selectedAsset.balance.toFixed(6)} ${selectedAsset.symbol}`)

        const tokenMintPubkey = new PublicKey(selectedAsset.mint)
        const vaultTokenAccount = await getAssociatedTokenAddress(tokenMintPubkey, vaultPda, true)
        const recipientTokenAccount = await getAssociatedTokenAddress(tokenMintPubkey, recipientPubkey)

        transferInstruction = createTransferInstruction(
          vaultTokenAccount,
          recipientTokenAccount,
          vaultPda,
          transferAmount, // 不包含精度
          [],
          TOKEN_PROGRAM_ID
        )
      }
      const { blockhash } = await Api.getBlockhash()
      const transferMessage = new TransactionMessage({ payerKey: vaultPda, recentBlockhash: blockhash, instructions: [transferInstruction] })
      const nextTransactionIndex = BigInt(currentMultisig.transactionIndex) + 1n

      // 创建交易并投票
      const createIx = multisig.instructions.vaultTransactionCreate({
        multisigPda,
        transactionIndex: nextTransactionIndex,
        creator: publicKey,
        vaultIndex: 0,
        ephemeralSigners: 0,
        transactionMessage: transferMessage,
        memo: `Transfer ${values.amount} to ${values.recipient}`,
        programId: SQUADS_PROGRAM_ID_V4_PUBKEY,
      })

      const proposalIx = multisig.instructions.proposalCreate({
        multisigPda,
        transactionIndex: nextTransactionIndex,
        creator: publicKey,
        isDraft: false,
        programId: SQUADS_PROGRAM_ID_V4_PUBKEY,
      })

      const approveIx = multisig.instructions.proposalApprove({
        multisigPda,
        transactionIndex: nextTransactionIndex,
        member: publicKey,
        programId: SQUADS_PROGRAM_ID_V4_PUBKEY,
      })

      const combinedMessage = new TransactionMessage({
        payerKey: publicKey,
        recentBlockhash: blockhash,
        instructions: [createIx, proposalIx, approveIx],
      })
      const combinedTx = new VersionedTransaction(combinedMessage.compileToV0Message())

      // phantom 签名
      const signedTx = await signTransaction(combinedTx)

      // 广播上链
      const { result: { value: { err } } } = await Api.createTransfer({ signedTransaction: Buffer.from(signedTx.serialize()).toString('base64') })
      if (err) {
        messageApi.error(`transaction failed, error: ${err}`)
      } else {
        messageApi.success('transaction success').then(() => history.push('/transactions'))
      }
    } catch (error: any) {
      console.error(`transaction failed: ${error.message}`)
      messageApi.error(`transaction failed: ${error.message}`)
    } finally {
      setLoading(false)
    }
  }, [publicKey, signTransaction, currentMultisig, connection])

  return (
    <>
      {contextHolder}
      <div>
        <Card loading={multisigLoading}>
          <Form form={form} layout="vertical" onFinish={handleTransfer}>
            <Form.Item label="From">
              <Input value={currentMultisig?.vault.address || ''} disabled placeholder="金库地址" style={{ backgroundColor: '#f5f5f5' }} />
            </Form.Item>

            <Form.Item name="recipient" label="To" rules={[{ required: true, message: '请选择收款地址' }]}>
              <Select placeholder="选择收款地址" loading={recipientsLoading} disabled={loading} optionFilterProp="children">
                {recipients.map(address => (
                  <Option key={address} value={address}>
                    <div>
                      <div style={{ fontSize: '12px', color: '#666' }}>{address}</div>
                    </div>
                  </Option>
                ))}
              </Select>
            </Form.Item>

            <Form.Item label="Amount">
              <Space.Compact style={{ width: '100%' }}>
                <Form.Item name="amount" noStyle rules={[{ required: true, message: '请输入转账金额' }]}>
                  <InputNumber placeholder="0" disabled={loading} style={{ width: '70%' }} />
                </Form.Item>
                <Form.Item name="selectedToken" noStyle rules={[{ required: true, message: '请选择币种' }]}>
                  <Select placeholder="请选择币种" disabled={loading} style={{ width: '30%' }}>
                    {currentMultisig?.vault.assets.map((asset) => (
                      <Option key={asset.symbol} value={asset.symbol}>
                        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                          <span>{asset.symbol}</span>
                          <span style={{ fontSize: '12px', color: '#666' }}>余额: {asset.balance.toFixed(6)} {asset.symbol}</span>
                        </div>
                      </Option>
                    ))}
                  </Select>
                </Form.Item>
              </Space.Compact>
            </Form.Item>

            <Form.Item>
              <Button type="primary" htmlType="submit" loading={loading} disabled={!publicKey || !currentMultisig || loading} icon={<SendOutlined />} block>
                创建转账交易
              </Button>
            </Form.Item>
          </Form>
        </Card>
      </div>
    </>
  )
}

export default SendPage